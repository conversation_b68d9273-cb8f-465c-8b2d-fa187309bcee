package logic

import (
	"app_service/apps/business/story/define/enums"
	"app_service/apps/platform/user/facade"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"time"

	"app_service/apps/business/story/dal/model"
	"app_service/apps/business/story/repo"
	"app_service/apps/platform/common/constant"
	"app_service/global"
	"app_service/pkg/search"
	"app_service/pkg/util"

	log "e.coding.net/g-dtay0385/common/go-logger"
	"github.com/go-redis/redis/v8"
)

const (
	cacheExpiration           = 24 * time.Hour // 缓存过期时间
	limit                     = 20
	storyUserNamesCacheExpire = 5 * time.Minute // 缓存过期时间
)

func GetStoryNumByScene(ctx context.Context) (storyNumMap map[int64]int32, err error) {
	storySchema := repo.GetQuery().Story
	list, err := repo.NewStoryRepo(storySchema.WithContext(ctx)).GetStoryNumByScene()
	if err != nil {
		return nil, err
	}
	storyNumMap = make(map[int64]int32)
	for _, v := range list {
		storyNumMap[v.SceneId] = v.StoryNum
	}
	return storyNumMap, nil
}

// GetStoryUserNameLastTwentyByScenesWithCache 批量获取场景最后20个用户名（带缓存）
func GetStoryUserNameLastTwentyByScenesWithCache(ctx context.Context, sceneIdList []int64) (map[int64][]string, error) {
	if len(sceneIdList) == 0 {
		return nil, nil
	}

	// 1. 准备缓存键
	cacheKeys := make([]string, len(sceneIdList))
	sceneIdToCacheKey := make(map[int64]string, len(sceneIdList))
	for i, sceneId := range sceneIdList {
		cacheKey := constant.GetStorySceneUserNameListKey(sceneId)
		cacheKeys[i] = cacheKey
		sceneIdToCacheKey[sceneId] = cacheKey
	}

	// 2. 批量获取缓存
	results, err := global.REDIS.MGet(ctx, cacheKeys...).Result()
	if err != nil && !errors.Is(err, redis.Nil) {
		log.Ctx(ctx).Errorf("批量获取用户名列表缓存失败 sceneIds:%v, err:%v", sceneIdList, err)
		// 不返回错误，继续从DB获取
	}

	// 3. 处理缓存结果
	resultMap := make(map[int64][]string)
	var missingSceneIds []int64

	for i, res := range results {
		sceneId := sceneIdList[i]

		if res == nil {
			missingSceneIds = append(missingSceneIds, sceneId)
			continue
		}

		var userNameList []string
		if err := json.Unmarshal([]byte(util.StrVal(res)), &userNameList); err != nil {
			log.Ctx(ctx).Errorf("解析缓存用户名列表失败 sceneId:%d, err:%v", sceneId, err)
			missingSceneIds = append(missingSceneIds, sceneId)
			continue
		}

		resultMap[sceneId] = userNameList
	}

	// 4. 如果有缓存未命中的场景ID，从数据库获取
	if len(missingSceneIds) > 0 {
		for _, sceneId := range missingSceneIds {
			userNameList, err := GetStoryUserNameLastTwentyBySceneWithCache(ctx, sceneId)
			if err != nil {
				log.Ctx(ctx).Errorf("从数据库获取用户名列表失败 sceneId:%d, err:%v", sceneId, err)
				continue
			}
			resultMap[sceneId] = userNameList
		}

	}
	return resultMap, nil
}

// GetStoryUserNameLastTwentyBySceneWithCache 获取场景最后20个用户名（带缓存）
func GetStoryUserNameLastTwentyBySceneWithCache(ctx context.Context, sceneId int64) ([]string, error) {
	cacheKey := constant.GetStorySceneUserNameListKey(sceneId)

	// 1. 先尝试从缓存获取
	result, err := global.REDIS.Get(ctx, cacheKey).Result()
	if err == nil {
		var userNameList []string
		if err := json.Unmarshal([]byte(result), &userNameList); err != nil {
			log.Ctx(ctx).Errorf("解析缓存用户名列表失败 sceneId:%d, err:%v", sceneId, err)
			// 不返回错误，继续从DB获取
		} else {
			return userNameList, nil
		}
	} else if !errors.Is(err, redis.Nil) {
		log.Ctx(ctx).Errorf("获取缓存用户名列表失败 sceneId:%d, err:%v", sceneId, err)
		// 不返回错误，继续从DB获取
	}

	// 2. 缓存未命中或出错，从数据库获取
	userNameList, err := GetStoryUserNameLastTwentyBySceneInDB(ctx, sceneId)
	if err != nil {
		return nil, fmt.Errorf("从数据库获取用户名列表失败: %w", err)
	}

	// 3. 异步更新缓存
	go func() {
		cacheCtx, cancel := context.WithTimeout(context.Background(), 2*time.Second)
		defer cancel()

		jsonData, err := json.Marshal(userNameList)
		if err != nil {
			log.Ctx(cacheCtx).Errorf("序列化用户名列表失败 sceneId:%d, err:%v", sceneId, err)
			return
		}

		if _, err := global.REDIS.Set(cacheCtx, cacheKey, jsonData, storyUserNamesCacheExpire).Result(); err != nil {
			log.Ctx(cacheCtx).Errorf("更新用户名列表缓存失败 sceneId:%d, err:%v", sceneId, err)
		}
	}()

	return userNameList, nil
}

// GetStoryUserNameLastTwentyBySceneInDB 获取故事场景参与的用户名
func GetStoryUserNameLastTwentyBySceneInDB(ctx context.Context, sceneId int64) (userNameList []string, err error) {
	// 查询上架且未结束的活动
	storySchema := repo.GetQuery().Story
	builder := search.NewQueryBuilder().Eq(storySchema.Status, enums.StoryStatusUp.Val()).
		Eq(storySchema.SceneID, sceneId).
		Gt(storySchema.EndTime, time.Now())
	storyList, err := repo.NewStoryRepo(storySchema.WithContext(ctx)).SelectList(builder.Build())
	if err != nil {
		return nil, err
	}
	list := make([]string, 0)
	if len(storyList) == 0 {
		return list, nil
	}
	storyIds := make([]int64, 0)
	for _, item := range storyList {
		storyIds = append(storyIds, item.ID)
	}
	storyOrderSchema := repo.GetQuery().StoryOrder
	userIdList, err := repo.NewStoryOrderRepo(storyOrderSchema.WithContext(ctx)).GetStoryUserNameLastBySceneInDB(sceneId, storyIds, limit)
	if err != nil {
		return nil, err
	}
	users, err := facade.GetNodeUsers(ctx, userIdList)
	if err != nil {
		return nil, err
	}
	for _, user := range users {
		list = append(list, user.PatbgDetail.Nickname)
	}
	return list, nil
}

// DelStorySceneUserNameListCache 删除缓存
func DelStorySceneUserNameListCache(ctx context.Context, sceneId int64) error {
	if _, err := global.REDIS.Del(ctx, constant.GetStorySceneUserNameListKey(sceneId)).Result(); err != nil {
		log.Ctx(ctx).Errorf("删除缓存失败 key:%v, err:%v", constant.GetStorySceneUserNameListKey(sceneId), err)
		return err
	}
	return nil
}

// GetStoryScene 获取单个故事场景
func GetStoryScene(ctx context.Context, key int64) (*model.StoryScene, error) {
	cacheKey := constant.GetStorySceneKey(key)
	result, err := global.REDIS.Get(ctx, cacheKey).Result()
	if err != nil {
		if errors.Is(err, redis.Nil) {
			scene, err := loadStorySceneFromDB(ctx, key)
			if err != nil {
				return nil, err
			}
			if scene == nil {
				return nil, nil
			}

			if err := SetStorySceneCache(ctx, key, scene); err != nil {
				log.Ctx(ctx).Warnf("设置缓存失败 key:%d, err:%v", key, err)
				// 即使缓存失败也返回数据
			}
			return scene, nil
		}
		log.Ctx(ctx).Errorf("获取缓存失败 key:%d, err:%v", key, err)
		return nil, err
	}

	var scene model.StoryScene
	if err := json.Unmarshal([]byte(result), &scene); err != nil {
		log.Ctx(ctx).Errorf("解析缓存数据失败 key:%d, err:%v", key, err)
		return nil, err
	}
	return &scene, nil
}

// GetStoryScenes 批量获取故事场景
func GetStoryScenes(ctx context.Context, keys ...int64) (map[int64]*model.StoryScene, error) {
	if len(keys) == 0 {
		return nil, nil
	}

	// 准备缓存键
	cacheKeys := make([]string, len(keys))
	for i, k := range keys {
		cacheKeys[i] = constant.GetStorySceneKey(k)
	}

	// 批量获取缓存
	results, err := global.REDIS.MGet(ctx, cacheKeys...).Result()
	if err != nil && !errors.Is(err, redis.Nil) {
		log.Ctx(ctx).Errorf("批量获取缓存失败 keys:%v, err:%v", keys, err)
		return nil, err
	}

	// 处理结果
	dataList := make(map[int64]*model.StoryScene)
	var missingKeys []int64

	for i, res := range results {
		if res == nil {
			missingKeys = append(missingKeys, keys[i])
			continue
		}

		var scene model.StoryScene
		if err := json.Unmarshal([]byte(util.StrVal(res)), &scene); err != nil {
			log.Ctx(ctx).Errorf("解析缓存数据失败 key:%d, err:%v", keys[i], err)
			return nil, err
		}
		dataList[keys[i]] = &scene
	}

	// 如果有缓存未命中的key，从数据库加载
	if len(missingKeys) > 0 {
		scenes, err := loadStoryScenesFromDB(ctx, missingKeys...)
		if err != nil {
			return nil, err
		}

		// 处理从数据库加载的数据
		for _, scene := range scenes {
			if scene == nil {
				continue
			}

			dataList[scene.ID] = scene

			// 异步更新缓存
			go func(s *model.StoryScene) {
				_ = SetStorySceneCache(context.Background(), s.ID, s)
			}(scene)
		}
	}

	return dataList, nil
}

// loadStorySceneFromDB 从数据库加载单个故事场景
func loadStorySceneFromDB(ctx context.Context, key int64) (*model.StoryScene, error) {
	storySceneSchema := repo.GetQuery().StoryScene
	builder := search.NewQueryBuilder().Eq(storySceneSchema.ID, key)

	scene, err := repo.NewStorySceneRepo(storySceneSchema.WithContext(ctx)).SelectOne(builder.Build())
	if err != nil {
		log.Ctx(ctx).Errorf("从数据库加载故事场景失败 key:%d, err:%v", key, err)
		return nil, err
	}

	return scene, nil
}

// loadStoryScenesFromDB 从数据库批量加载故事场景
func loadStoryScenesFromDB(ctx context.Context, keys ...int64) ([]*model.StoryScene, error) {
	storySceneSchema := repo.GetQuery().StoryScene
	builder := search.NewQueryBuilder().In(storySceneSchema.ID, keys)

	scenes, err := repo.NewStorySceneRepo(storySceneSchema.WithContext(ctx)).SelectList(builder.Build())
	if err != nil {
		log.Ctx(ctx).Errorf("从数据库批量加载故事场景失败 keys:%v, err:%v", keys, err)
		return nil, err
	}

	return scenes, nil
}

// SetStorySceneCache 设置缓存
func SetStorySceneCache(ctx context.Context, key int64, val interface{}) error {
	jsonData, err := json.Marshal(val)
	if err != nil {
		log.Ctx(ctx).Errorf("序列化缓存数据失败 key:%d, err:%v", key, err)
		return err
	}

	if _, err := global.REDIS.Set(ctx, constant.GetStorySceneKey(key), jsonData, cacheExpiration).Result(); err != nil {
		log.Ctx(ctx).Errorf("设置缓存失败 key:%d, err:%v", key, err)
		return err
	}

	return nil
}
