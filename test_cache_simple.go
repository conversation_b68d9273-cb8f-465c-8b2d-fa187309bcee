package main

import (
	"context"
	"fmt"
	"time"

	"app_service/pkg/cache"
	"app_service/global"

	"github.com/go-redis/redis/v8"
)

func main() {
	// 简单测试缓存函数
	global.REDIS = redis.NewClient(&redis.Options{
		Addr: "localhost:6379",
		DB:   0,
	})

	ctx := context.Background()

	// 测试 SetToCache 和 GetFromCache
	testData := map[string]string{"test": "value"}
	err := cache.SetToCache(ctx, "test:key", testData, time.Minute)
	if err != nil {
		fmt.Printf("SetToCache error: %v\n", err)
		return
	}

	var result map[string]string
	err = cache.GetFromCache(ctx, "test:key", &result)
	if err != nil {
		fmt.Printf("GetFromCache error: %v\n", err)
		return
	}

	fmt.Printf("Cache test successful: %+v\n", result)
}
