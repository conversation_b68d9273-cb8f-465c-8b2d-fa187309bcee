package cache

import (
	"context"
	"errors"
	"math/rand"
	"time"

	"app_service/global"
	"app_service/pkg/util"

	log "e.coding.net/g-dtay0385/common/go-logger"
	"github.com/go-redis/redis/v8"
)

const (
	// NullCacheValue 空值缓存标记
	NullCacheValue = "__NULL_CACHE__"
	// NullCacheTTL 空值缓存过期时间（较短，避免长时间缓存错误的空值）
	NullCacheTTL = 2 * time.Minute
)

// CacheOptions 缓存选项
type CacheOptions struct {
	// BaseTTL 基础过期时间
	BaseTTL time.Duration
	// RandomFactor 随机因子，范围0-1，实际TTL = BaseTTL + random(0, BaseTTL * RandomFactor)
	RandomFactor float64
	// EnableNullCache 是否启用空值缓存
	EnableNullCache bool
}

// DefaultCacheOptions 默认缓存选项
func DefaultCacheOptions(baseTTL time.Duration) *CacheOptions {
	return &CacheOptions{
		BaseTTL:         baseTTL,
		RandomFactor:    0.1, // 10%的随机时间
		EnableNullCache: true,
	}
}

// GetRandomTTL 获取随机TTL，防止缓存雪崩
func (opts *CacheOptions) GetRandomTTL() time.Duration {
	if opts.RandomFactor <= 0 {
		return opts.BaseTTL
	}

	// 计算随机时间：0 到 BaseTTL * RandomFactor
	randomDuration := time.Duration(float64(opts.BaseTTL) * opts.RandomFactor * rand.Float64())
	return opts.BaseTTL + randomDuration
}

// SetCache 设置缓存，支持随机TTL，使用MessagePack序列化
func SetCache(ctx context.Context, key string, value interface{}, opts *CacheOptions) error {
	// 使用MessagePack序列化，性能更好，存储空间更小
	msgpackData, err := util.MsgpackMarshal(value)
	if err != nil {
		log.Ctx(ctx).Errorf("MessagePack序列化缓存数据失败 key:%s, err:%v", key, err)
		return err
	}

	ttl := opts.GetRandomTTL()
	if err := global.REDIS.Set(ctx, key, msgpackData, ttl).Err(); err != nil {
		log.Ctx(ctx).Errorf("设置缓存失败 key:%s, ttl:%v, err:%v", key, ttl, err)
		return err
	}

	return nil
}

// GetCache 获取缓存，支持空值缓存检测，使用MessagePack反序列化
func GetCache(ctx context.Context, key string, result interface{}) (bool, error) {
	cachedData, err := global.REDIS.Get(ctx, key).Result()

	switch {
	case err == nil:
		// 检查是否为空值缓存
		if cachedData == NullCacheValue {
			return false, nil // 返回false表示数据不存在（但缓存命中了空值）
		}

		// 正常缓存，使用MessagePack反序列化数据
		if err := util.MsgpackUnmarshal([]byte(cachedData), result); err != nil {
			log.Ctx(ctx).Errorf("MessagePack解析缓存数据失败 key:%s, err:%v", key, err)
			return false, err
		}
		return true, nil

	case errors.Is(err, redis.Nil):
		// 缓存未命中
		return false, nil

	default:
		log.Ctx(ctx).Errorf("获取缓存失败 key:%s, err:%v", key, err)
		return false, err
	}
}

// SetNullCache 设置空值缓存，防止缓存穿透
func SetNullCache(ctx context.Context, key string) error {
	if err := global.REDIS.Set(ctx, key, NullCacheValue, NullCacheTTL).Err(); err != nil {
		log.Ctx(ctx).Errorf("设置空值缓存失败 key:%s, err:%v", key, err)
		return err
	}
	return nil
}

// IsNullCache 检查是否为空值缓存
func IsNullCache(value string) bool {
	return value == NullCacheValue
}

// GetFromCache 通用缓存获取函数（兼容marketplace_service风格）
func GetFromCache(ctx context.Context, cacheKey string, target interface{}) error {
	result, err := global.REDIS.Get(ctx, cacheKey).Result()
	if err != nil {
		if errors.Is(err, redis.Nil) {
			return err
		}
		log.Ctx(ctx).Errorf("Redis 获取数据失败 key:%s, err:%v", cacheKey, err)
		return errors.New("缓存服务异常")
	}

	// 检查空值缓存
	if result == NullCacheValue {
		return errors.New("数据不存在")
	}

	if err := util.MsgpackUnmarshal([]byte(result), target); err != nil {
		log.Ctx(ctx).Errorf("缓存数据解析失败 key:%s, err:%v", cacheKey, err)
		return errors.New("缓存数据格式异常")
	}
	return nil
}

// SetToCache 通用缓存设置函数（兼容marketplace_service风格）
func SetToCache(ctx context.Context, cacheKey string, value interface{}, ttl time.Duration) error {
	cacheData, err := util.MsgpackMarshal(value)
	if err != nil {
		return errors.New("序列化缓存数据失败: " + err.Error())
	}
	if err := global.REDIS.Set(ctx, cacheKey, cacheData, ttl).Err(); err != nil {
		log.Ctx(ctx).Errorf("Redis 写入数据失败 key:%s, err:%v", cacheKey, err)
		return errors.New("缓存服务异常")
	}
	return nil
}
